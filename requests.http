### GET all customers
GET http://localhost:8081/customers
Accept: application/json

### POST - Create new customer
POST http://localhost:8081/customers
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

### GET customer by ID with projection
GET http://localhost:8081/customers/1?projection=fullCustomer
Accept: application/json

### PUT - Update customer
PUT http://localhost:8081/customers/1
Content-Type: application/json

{
  "name": "John Updated",
  "email": "<EMAIL>"
}

### DELETE customer
DELETE http://localhost:8081/customers/1



###########################################################################
# INVENTORY SERVICE (Port 8082)
###########################################################################

### GET all products
GET http://localhost:8082/products
Accept: application/json

### GET product by ID with projection
GET http://localhost:8082/products/1?projection=fullProduct
Accept: application/json

###########################################################################
# ORDER SERVICE (Port 8083)
###########################################################################

### GET all orders (with list projection)
GET http://localhost:8083/orders
Accept: application/json

### GET all orders with full projection
GET http://localhost:8083/orders?projection=fullOrder
Accept: application/json

### GET all orders with summary projection
GET http://localhost:8083/orders?projection=orderSummary
Accept: application/json

### GET order by ID
GET http://localhost:8083/orders/1
Accept: application/json

### GET order by ID with full projection
GET http://localhost:8083/orders/1?projection=fullOrder
Accept: application/json

### GET order by ID with summary projection
GET http://localhost:8083/orders/1?projection=orderSummary
Accept: application/json

### GET full order details (with customer and product info) - Legacy
GET http://localhost:8083/fullOrder/7
Accept: application/json

###########################################################################
# ORDER API ENDPOINTS (Custom Controller)
###########################################################################

### GET all orders via API
GET http://localhost:8083/api/orders
Accept: application/json

### GET orders by status
GET http://localhost:8083/api/orders/status/PENDING
Accept: application/json

### GET orders by customer
GET http://localhost:8083/api/orders/customer/1
Accept: application/json

### GET active orders
GET http://localhost:8083/api/orders/active
Accept: application/json

### GET order statistics
GET http://localhost:8083/api/orders/stats
Accept: application/json

### GET orders with filters
GET http://localhost:8083/api/orders?status=DELIVERED&customerId=1
Accept: application/json

###########################################################################
# GATEWAY SERVICE (Port 9999)
###########################################################################

### GET customers through gateway
GET http://localhost:9999/customer-service/customers
Accept: application/json

### GET products through gateway
GET http://localhost:9999/inventory-service/products
Accept: application/json

### GET orders through gateway
GET http://localhost:9999/order-service/orders
Accept: application/json

###########################################################################
# ACTUATOR ENDPOINTS
###########################################################################

### Customer Service Health
GET http://localhost:8081/actuator/health

### Inventory Service Health
GET http://localhost:8082/actuator/health

### Order Service Health
GET http://localhost:8083/actuator/health

### Config Service Health
GET http://localhost:8888/actuator/health

### Gateway Service Health
GET http://localhost:9999/actuator/health

###########################################################################
# REFRESH CONFIGURATION ENDPOINTS
###########################################################################

### Refresh Customer Service Configuration
POST http://localhost:8081/actuator/refresh
Content-Type: application/json

{}

### Refresh Inventory Service Configuration
POST http://localhost:8082/actuator/refresh
Content-Type: application/json

{}

### Refresh Order Service Configuration
POST http://localhost:8083/actuator/refresh
Content-Type: application/json

{}

### Refresh Gateway Service Configuration
POST http://localhost:9999/actuator/refresh
Content-Type: application/json

{}

### Refresh Config Service Configuration
POST http://localhost:8888/actuator/refresh
Content-Type: application/json

{}

###########################################################################
# AUTOMATION SCRIPTS
###########################################################################

### Master management script (RECOMMENDED)
# Execute: ./manage-services.sh [command]
# Commands: status, start, stop, restart, kill [service], refresh, open, quick-open

### Individual scripts:
# Check status:           ./status-services.sh
# Start all:              ./start-and-open-services.sh
# Stop all:               ./kill-all-services.sh
# Stop specific:          ./kill-service.sh [service_name|port]
# Refresh config:         ./refresh-all-services.sh
# Open interfaces:        ./open-services.sh
# Quick open:             ./quick-open-services.sh

### Examples:
# ./manage-services.sh status     # Check service status
# ./manage-services.sh start      # Start all services
# ./manage-services.sh stop       # Stop all services
# ./manage-services.sh kill consul # Stop only Consul
# ./manage-services.sh refresh    # Refresh configurations

###########################################################################
# SERVICE URLS
###########################################################################

### Direct access URLs:
# 1. Consul UI:         http://localhost:8500/ui
# 2. Config Service:    http://localhost:8888
# 3. Gateway:           http://localhost:9999
# 4. Customer Service:  http://localhost:8081
# 5. Inventory Service: http://localhost:8082
# 6. Order Service:     http://localhost:8083
