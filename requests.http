### GET request example
GET http://localhost:8081/customers
Accept: application/json

### POST request example
POST http://localhost:8081/actuator/refresh   
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

### PUT request example
PUT http://localhost:8081/api/customers/1
Content-Type: application/json

{
  "name": "John Updated",
  "email": "<EMAIL>"
}

### DELETE request example
DELETE http://localhost:8081/api/customers/1



###########################################################################""


### GET request example
GET http://localhost:8083/actuator/refresh
Accept: application/json


### POST request example
POST http://localhost:8083/actuator/refresh  

Content-Type: application/json
