### GET all customers
GET http://localhost:8081/customers
Accept: application/json

### POST - Create new customer
POST http://localhost:8081/customers
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}

### GET customer by ID with projection
GET http://localhost:8081/customers/1?projection=fullCustomer
Accept: application/json

### PUT - Update customer
PUT http://localhost:8081/customers/1
Content-Type: application/json

{
  "name": "John Updated",
  "email": "<EMAIL>"
}

### DELETE customer
DELETE http://localhost:8081/customers/1



###########################################################################
# INVENTORY SERVICE (Port 8082)
###########################################################################

### GET all products
GET http://localhost:8082/products
Accept: application/json

### GET product by ID with projection
GET http://localhost:8082/products/1?projection=fullProduct
Accept: application/json

###########################################################################
# ORDER SERVICE (Port 8083)
###########################################################################

### GET all orders
GET http://localhost:8083/orders
Accept: application/json

### GET full order details (with customer and product info)
GET http://localhost:8083/fullOrder/1
Accept: application/json

###########################################################################
# GATEWAY SERVICE (Port 9999)
###########################################################################

### GET customers through gateway
GET http://localhost:9999/customer-service/customers
Accept: application/json

### GET products through gateway
GET http://localhost:9999/inventory-service/products
Accept: application/json

### GET orders through gateway
GET http://localhost:9999/order-service/orders
Accept: application/json

###########################################################################
# ACTUATOR ENDPOINTS
###########################################################################

### Customer Service Health
GET http://localhost:8081/actuator/health

### Inventory Service Health
GET http://localhost:8082/actuator/health

### Order Service Health
GET http://localhost:8083/actuator/health

### Config Service Health
GET http://localhost:8888/actuator/health

### Gateway Service Health
GET http://localhost:9999/actuator/health

### Refresh Customer Service Configuration
POST http://localhost:8081/actuator/refresh
Content-Type: application/json

{}
