package tn.essat.customer_service;

import java.util.List;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;

import tn.essat.customer_service.entity.Customer;
import tn.essat.customer_service.repository.CustomerRepository;


@SpringBootApplication
@EnableDiscoveryClient

public class CustomerServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(CustomerServiceApplication.class, args);
	}


	@Bean
	CommandLineRunner start(CustomerRepository customerRepository) {

		return args -> {

			customerRepository.saveAll(List.of(

				Customer.builder().name("<PERSON>").email("<EMAIL>").build(),
				Customer.builder().name("<PERSON>").email("<EMAIL>").build(),
				Customer.builder().name("Imane").email("<EMAIL>").build()

			));

			customerRepository.findAll().forEach(System.out::println);
		};
	}


}

