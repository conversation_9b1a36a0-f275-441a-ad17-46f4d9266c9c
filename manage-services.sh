#!/bin/bash

# Master script for managing microservices

show_help() {
    echo "🚀 Microservices Management Tool"
    echo "================================"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  status, st           - Show status of all services"
    echo "  start, up            - Start all services and open interfaces"
    echo "  stop, down           - Stop all services"
    echo "  restart, reboot      - Restart all services"
    echo "  kill [service]       - Stop specific service"
    echo "  refresh, reload      - Refresh configuration of all services"
    echo "  open, browse         - Open service interfaces in browser"
    echo "  quick-open, qo       - Quick open all interfaces (no health check)"
    echo "  help, -h, --help     - Show this help"
    echo ""
    echo "Service names for 'kill' command:"
    echo "  consul, config, customer, inventory, order, gateway, all"
    echo "  Or use port numbers: 8500, 8888, 8081, 8082, 8083, 9999"
    echo ""
    echo "Examples:"
    echo "  $0 status            # Check service status"
    echo "  $0 start             # Start all services"
    echo "  $0 stop              # Stop all services"
    echo "  $0 kill consul       # Stop only Consul"
    echo "  $0 kill 8081         # Stop service on port 8081"
    echo "  $0 refresh           # Refresh all configurations"
    echo "  $0 open              # Open service interfaces"
    echo ""
}

# Check if script exists and is executable
check_script() {
    local script=$1
    if [ ! -f "$script" ]; then
        echo "❌ Script not found: $script"
        return 1
    fi
    if [ ! -x "$script" ]; then
        echo "❌ Script not executable: $script"
        echo "   Run: chmod +x $script"
        return 1
    fi
    return 0
}

# Execute command with error handling
execute_command() {
    local script=$1
    local args=$2
    
    if check_script "$script"; then
        echo "🔧 Executing: $script $args"
        echo ""
        if [ -n "$args" ]; then
            ./"$script" $args
        else
            ./"$script"
        fi
        local exit_code=$?
        echo ""
        if [ $exit_code -eq 0 ]; then
            echo "✅ Command completed successfully"
        else
            echo "❌ Command failed with exit code: $exit_code"
        fi
        return $exit_code
    else
        return 1
    fi
}

# Main logic
if [ $# -eq 0 ]; then
    show_help
    exit 0
fi

command="$1"
shift  # Remove first argument, keep the rest

case "$command" in
    "status"|"st")
        execute_command "status-services.sh"
        ;;
    "start"|"up")
        execute_command "start-and-open-services.sh"
        ;;
    "stop"|"down")
        execute_command "kill-all-services.sh"
        ;;
    "restart"|"reboot")
        echo "🔄 Restarting all services..."
        echo ""
        execute_command "kill-all-services.sh"
        if [ $? -eq 0 ]; then
            echo ""
            echo "⏳ Waiting 3 seconds before restart..."
            sleep 3
            echo ""
            execute_command "start-and-open-services.sh"
        fi
        ;;
    "kill")
        if [ $# -eq 0 ]; then
            echo "❌ Please specify a service to kill"
            echo "Usage: $0 kill [service_name|port|all]"
            exit 1
        fi
        execute_command "kill-service.sh" "$1"
        ;;
    "refresh"|"reload")
        execute_command "refresh-all-services.sh"
        ;;
    "open"|"browse")
        execute_command "open-services.sh"
        ;;
    "quick-open"|"qo")
        execute_command "quick-open-services.sh"
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $command"
        echo ""
        show_help
        exit 1
        ;;
esac
