#!/bin/bash

echo "📊 Microservices Status Check"
echo "============================="

# Array of services with their ports and names
services=(
    "Consul:8500:http://localhost:8500/v1/status/leader"
    "Config Service:8888:http://localhost:8888/actuator/health"
    "Customer Service:8081:http://localhost:8081/actuator/health"
    "Inventory Service:8082:http://localhost:8082/actuator/health"
    "Order Service:8083:http://localhost:8083/actuator/health"
    "Gateway Service:9999:http://localhost:9999/actuator/health"
)

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to get process ID using a port
get_pid_by_port() {
    local port=$1
    lsof -ti :$port 2>/dev/null
}

# Function to check service health
check_health() {
    local url=$1
    if curl -s --connect-timeout 3 "$url" > /dev/null 2>&1; then
        return 0  # Service is healthy
    else
        return 1  # Service is not healthy
    fi
}

# Function to get service uptime
get_uptime() {
    local pid=$1
    if [ -n "$pid" ] && kill -0 $pid 2>/dev/null; then
        local start_time=$(ps -o lstart= -p $pid 2>/dev/null | xargs -I {} date -d "{}" +%s 2>/dev/null)
        if [ -n "$start_time" ]; then
            local current_time=$(date +%s)
            local uptime_seconds=$((current_time - start_time))
            
            if [ $uptime_seconds -ge 3600 ]; then
                local hours=$((uptime_seconds / 3600))
                local minutes=$(((uptime_seconds % 3600) / 60))
                echo "${hours}h ${minutes}m"
            elif [ $uptime_seconds -ge 60 ]; then
                local minutes=$((uptime_seconds / 60))
                local seconds=$((uptime_seconds % 60))
                echo "${minutes}m ${seconds}s"
            else
                echo "${uptime_seconds}s"
            fi
        else
            echo "unknown"
        fi
    else
        echo "N/A"
    fi
}

# Function to check a single service
check_service() {
    local service_name=$1
    local port=$2
    local health_url=$3
    
    printf "%-20s " "$service_name:"
    
    if check_port $port; then
        local pids=$(get_pid_by_port $port)
        local main_pid=$(echo $pids | awk '{print $1}')
        
        printf "🟢 RUNNING "
        printf "(PID: %-8s" "$main_pid)"
        
        # Check health if URL provided
        if [ -n "$health_url" ]; then
            if check_health "$health_url"; then
                printf "✅ HEALTHY "
            else
                printf "⚠️  UNHEALTHY "
            fi
        fi
        
        # Show uptime
        local uptime=$(get_uptime "$main_pid")
        printf "⏱️  %s" "$uptime"
        
    else
        printf "🔴 STOPPED  "
        printf "%-10s " ""
        printf "❌ N/A     "
        printf "⏱️  N/A"
    fi
    
    echo ""
}

echo ""
echo "🔍 Checking all services..."
echo ""

# Check each service
for service in "${services[@]}"; do
    IFS=':' read -r name port health_url <<< "$service"
    check_service "$name" "$port" "$health_url"
done

echo ""
echo "📋 Summary:"

# Count running services
running_count=0
total_count=${#services[@]}

for service in "${services[@]}"; do
    IFS=':' read -r name port health_url <<< "$service"
    if check_port $port; then
        ((running_count++))
    fi
done

echo "   Running: $running_count/$total_count services"

if [ $running_count -eq $total_count ]; then
    echo "   Status: 🎉 All services are running!"
elif [ $running_count -eq 0 ]; then
    echo "   Status: 🛑 All services are stopped"
else
    echo "   Status: ⚠️  Some services are not running"
fi

echo ""
echo "🌐 Service URLs:"
echo "   • Consul UI:         http://localhost:8500/ui"
echo "   • Config Service:    http://localhost:8888"
echo "   • Customer Service:  http://localhost:8081"
echo "   • Inventory Service: http://localhost:8082"
echo "   • Order Service:     http://localhost:8083"
echo "   • Gateway Service:   http://localhost:9999"

echo ""
echo "🛠️  Available commands:"
echo "   • Start all:         ./start-and-open-services.sh"
echo "   • Stop all:          ./kill-all-services.sh"
echo "   • Stop specific:     ./kill-service.sh [service_name]"
echo "   • Open interfaces:   ./open-services.sh"
echo "   • Refresh config:    ./refresh-all-services.sh"
