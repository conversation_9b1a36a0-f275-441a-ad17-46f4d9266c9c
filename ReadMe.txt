


	1- <PERSON><PERSON><PERSON><PERSON> Consul d'abord :
	
		consul agent -dev -ui -client=0.0.0.0
	
	
	
	2- Pour vérifier l'état de Consul :
	
		Interface web : http://localhost:8500
		API : curl http://localhost:8500/v1/status/leader


	
	3- Démarrons le Config Service :
	
		cd config-service && mvn spring-boot:run
		
		
	
	4- Config Service - Démarrage réussi !
	
		Informations importantes :

		✅ Démarré : Started ConfigServiceApplication in 2.927 seconds
		✅ Port : 8888 (Tomcat started on port 8888)
		✅ Consul : Service enregistré avec succès dans Consul
		✅ Health Check : Consul surveille la santé via http://************:8888/actuator/health
		✅ Configuration : Utilise les fichiers locaux dans /home/<USER>/Bureau/magasin-workspace/config-repo/
	
	
	
	5- État final des services :
		
		Service			Status		Port	Consul		Health
	     -----------------------------------------------------------------------------
		Consul			✅ Running	8500	-		✅
		Config Service		✅ Running	8888	✅ Registered	✅ UP
		Gateway Service		✅ Running	9999	✅ Registered	✅ UP
		Customer Service	✅ Running	8081	✅ Registered	✅ UP
		Inventory Service	✅ Running	8082	✅ Registered	✅ UP
		Order Service		✅ Running	8083	✅ Registered	✅ UP
		
		
		
		
	6- 🌐 Accès aux services :
	
		Consul UI : 		http://localhost:8500/ui
		Config Service : 	http://localhost:8888
		Gateway : 		http://localhost:9999
		Customer Service : 	http://localhost:8081
		Inventory Service : 	http://localhost:8082
		Order Service : 	http://localhost:8083
		
		
		
		
		
	7- 🎉  Refresh de tous les services par le script suivant :
	
		./refresh-all-services.sh
	
		
	
	
	
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
