


	1- <PERSON><PERSON><PERSON><PERSON> Consul d'abord :
	
		consul agent -dev -ui -client=0.0.0.0
	
	
	
	2- Pour vérifier l'état de Consul :
	
		Interface web : http://localhost:8500
		API : curl http://localhost:8500/v1/status/leader


	
	3- Démarrons le Config Service :
	
		cd config-service && mvn spring-boot:run
		
		
	
	4- Config Service - Démarrage réussi !
	
		Informations importantes :

		✅ Démarré : Started ConfigServiceApplication in 2.927 seconds
		✅ Port : 8888 (Tomcat started on port 8888)
		✅ Consul : Service enregistré avec succès dans Consul
		✅ Health Check : Consul surveille la santé via http://************:8888/actuator/health
		✅ Configuration : Utilise les fichiers locaux dans /home/<USER>/Bureau/magasin-workspace/config-repo/
	
	
	
	5- État final des services :
		
		Service			Status		Port	Consul		Health
	     -----------------------------------------------------------------------------
		Consul			✅ Running	8500	-		✅
		Config Service		✅ Running	8888	✅ Registered	✅ UP
		Gateway Service		✅ Running	9999	✅ Registered	✅ UP
		Customer Service	✅ Running	8081	✅ Registered	✅ UP
		Inventory Service	✅ Running	8082	✅ Registered	✅ UP
		Order Service		✅ Running	8083	✅ Registered	✅ UP
		
		
		
		
	6- 🌐 Accès aux services :
	
		Consul UI : 		http://localhost:8500/ui
		Config Service : 	http://localhost:8888
		Gateway : 		http://localhost:9999
		Customer Service : 	http://localhost:8081
		Inventory Service : 	http://localhost:8082
		Order Service : 	http://localhost:8083
		
		
		
		
		
	7- 🎉  Refresh de tous les services par le script suivant :
	
		./refresh-all-services.sh
	
		
	8- 💡 To start services again, use :
   
   		./start-and-open-services.sh
   		
   		
	9- 🛑 Stopping all microservices :

		./kill-all-services.sh
	
	
	10- 🛑 Kill Specific Service
		=======================

		Usage: ./kill-service.sh [service_name|port]

		Available services:
		  consul          - Consul (port 8500)
		  config          - Config Service (port 8888)
		  customer        - Customer Service (port 8081)
		  inventory       - Inventory Service (port 8082)
		  order           - Order Service (port 8083)
		  gateway         - Gateway Service (port 9999)
		  all             - All services

		Examples:
		  ./kill-service.sh consul       # Stop Consul
		  ./kill-service.sh 8081         # Stop service on port 8081
		  ./kill-service.sh all          # Stop all services
				
		
		
	11- 📊 Microservices Status Check
		=============================
		
		./status-services.sh
		
		
		
	🔍 Checking all services...

		Consul:              🔴 STOPPED             ❌ N/A     ⏱️  N/A
		Config Service:      🔴 STOPPED             ❌ N/A     ⏱️  N/A
		Customer Service:    🔴 STOPPED             ❌ N/A     ⏱️  N/A
		Inventory Service:   🔴 STOPPED             ❌ N/A     ⏱️  N/A
		Order Service:       🔴 STOPPED             ❌ N/A     ⏱️  N/A
		Gateway Service:     🔴 STOPPED             ❌ N/A     ⏱️  N/A


	📋 Summary:
	
	   Running: 0/6 services
	   Status: 🛑 All services are stopped



	🌐 Service URLs:
	
	   • Consul UI:         http://localhost:8500/ui
	   • Config Service:    http://localhost:8888
	   • Customer Service:  http://localhost:8081
	   • Inventory Service: http://localhost:8082
	   • Order Service:     http://localhost:8083
	   • Gateway Service:   http://localhost:9999



	🛠️  Available commands:
	
	   • Start all:         ./start-and-open-services.sh
	   • Stop all:          ./kill-all-services.sh
	   • Stop specific:     ./kill-service.sh [service_name|port]
	   • Open interfaces:   ./open-services.sh
	   • Refresh config:    ./refresh-all-services.sh



####################################################################################################################



	🚀 Workflows recommandés :
	
		Démarrage quotidien :
		____________________
			
			./manage-services.sh status    # Vérifier l'état	
			
			./manage-services.sh start     # Tout démarrer



		Développement :
		_______________
		
			./manage-services.sh refresh   # Après changements config
			
			./manage-services.sh kill order # Debug service spécifique
			
			
			
		Fin de journée :
		________________
		
			./manage-services.sh stop      # Tout arrêter
			
			
			
			
################################################################################################################################



	📚 Documentation :
	
		QUICK_START.md - Guide de démarrage rapide
		
		SCRIPTS_README.md - Documentation complète
		
		requests.http - Mis à jour avec tous les scripts































			
			
			
			
			
			
			
			
			
			
			
			
			
			

























		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
