# 🚀 Microservices Automation Scripts

This directory contains automation scripts to help manage your microservices architecture.

## 📋 Available Scripts

### 📊 Service Management

#### `status-services.sh`
Displays the current status of all microservices.

```bash
./status-services.sh
```

**Features:**
- ✅ Shows running/stopped status for each service
- ✅ Displays process IDs and uptime
- ✅ Health check status for each service
- ✅ Summary of running services
- ✅ Quick access to service URLs and commands

#### `kill-all-services.sh`
Stops all running microservices.

```bash
./kill-all-services.sh
```

**Features:**
- ✅ Stops services in reverse order (opposite of startup)
- ✅ Graceful shutdown with fallback to force kill
- ✅ Comprehensive cleanup of Java/Maven processes
- ✅ Final verification of stopped services
- ✅ Detailed logging of each step

#### `kill-service.sh`
Stops a specific service by name or port.

```bash
./kill-service.sh [service_name|port]
```

**Examples:**
```bash
./kill-service.sh consul     # Stop Consul
./kill-service.sh 8081       # Stop service on port 8081
./kill-service.sh all        # Stop all services
```

**Features:**
- ✅ Selective service stopping
- ✅ Support for service names and port numbers
- ✅ Shows current service status
- ✅ Graceful shutdown with force kill fallback

---

### 🔄 Configuration Management

#### `refresh-all-services.sh`
Refreshes the configuration of all running microservices.

```bash
./refresh-all-services.sh
```

**Features:**
- ✅ Checks service availability before attempting refresh
- ✅ Calls `/actuator/refresh` endpoint for each service
- ✅ Shows detailed status for each service
- ✅ Displays changed properties (if any)

---

### 🌐 Browser Interface Management

#### `open-services.sh`
Opens all service interfaces in your default browser with health checks.

```bash
./open-services.sh
```

**Features:**
- ✅ Checks if each service is running before opening
- ✅ Opens URLs in the correct order
- ✅ Skips services that are not available
- ✅ Cross-platform browser detection (Linux, macOS, Windows)

#### `quick-open-services.sh`
Quickly opens all service interfaces without health checks.

```bash
./quick-open-services.sh
```

**Features:**
- ✅ Fast opening without waiting for health checks
- ✅ Opens all URLs regardless of service status
- ✅ Useful when you know all services are running

---

### 🚀 Complete Startup Management

#### `start-and-open-services.sh`
Starts all services and opens their interfaces (comprehensive solution).

```bash
./start-and-open-services.sh
```

**Features:**
- ✅ Starts Consul if not running
- ✅ Starts all microservices in the correct order
- ✅ Waits for each service to be ready before starting the next
- ✅ Opens all interfaces after successful startup
- ⚠️  **Note:** This is a comprehensive script that may take several minutes

---

## 🎯 Service Order

All scripts follow this service order:

1. **Consul UI** - `http://localhost:8500/ui`
2. **Config Service** - `http://localhost:8888`
3. **Gateway** - `http://localhost:9999`
4. **Customer Service** - `http://localhost:8081`
5. **Inventory Service** - `http://localhost:8082`
6. **Order Service** - `http://localhost:8083`

---

## 🛠️ Usage Examples

### Complete Development Workflow

```bash
# 1. Check current status
./status-services.sh

# 2. Start all services and open interfaces
./start-and-open-services.sh

# 3. After making configuration changes, refresh all services
./refresh-all-services.sh

# 4. Stop all services when done
./kill-all-services.sh
```

### Daily Development

```bash
# Check what's running
./status-services.sh

# If services are already running, just open interfaces
./quick-open-services.sh

# Stop a specific service for debugging
./kill-service.sh order

# Restart just that service
cd order-service && mvn spring-boot:run

# Refresh configuration after changes
./refresh-all-services.sh
```

### Debugging Workflow

```bash
# Check service status
./status-services.sh

# Stop problematic service
./kill-service.sh customer

# Check logs and restart manually
cd customer-service && mvn spring-boot:run

# Or restart all services
./kill-all-services.sh
./start-and-open-services.sh
```

---

## 🔧 Requirements

### System Requirements
- **Linux/macOS/Windows** with bash support
- **curl** - for health checks
- **lsof** - for port checking (Linux/macOS)
- **Maven** - for starting services
- **Consul** - must be installed and available in PATH

### Browser Support
- **Linux**: Uses `xdg-open`
- **macOS**: Uses `open`
- **Windows**: Uses `start` (Git Bash/WSL)

---

## 📊 Script Comparison

| Script | Purpose | Health Checks | Starts Services | Stops Services | Opens Browser | Speed |
|--------|---------|---------------|-----------------|----------------|---------------|-------|
| `status-services.sh` | Status Check | ✅ | ❌ | ❌ | ❌ | Fast |
| `start-and-open-services.sh` | Full Startup | ✅ | ✅ | ❌ | ✅ | Slow |
| `kill-all-services.sh` | Stop All | ✅ | ❌ | ✅ | ❌ | Medium |
| `kill-service.sh` | Stop Specific | ✅ | ❌ | ✅ | ❌ | Fast |
| `refresh-all-services.sh` | Config Refresh | ✅ | ❌ | ❌ | ❌ | Fast |
| `open-services.sh` | Open Interfaces | ✅ | ❌ | ❌ | ✅ | Medium |
| `quick-open-services.sh` | Quick Open | ❌ | ❌ | ❌ | ✅ | Fast |

---

## 🐛 Troubleshooting

### Script Permission Issues
```bash
chmod +x *.sh
```

### Browser Not Opening
- Check if your system has `xdg-open` (Linux), `open` (macOS), or `start` (Windows)
- Manually copy URLs from script output

### Service Health Check Failures
- Ensure services are running on expected ports
- Check firewall settings
- Verify service configurations

---

## 📝 Notes

- All scripts include detailed logging and status information
- Scripts are designed to be safe and non-destructive
- Cross-platform compatibility for major operating systems
- Configurable delays between operations to avoid overwhelming services
