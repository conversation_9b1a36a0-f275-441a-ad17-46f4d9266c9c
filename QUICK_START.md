# 🚀 Quick Start Guide - Microservices Management

## 🎯 One Command to Rule Them All

Use the **master script** for all operations:

```bash
./manage-services.sh [command]
```

## 📋 Essential Commands

| Command | Description | Example |
|---------|-------------|---------|
| `status` | Check service status | `./manage-services.sh status` |
| `start` | Start all services + open interfaces | `./manage-services.sh start` |
| `stop` | Stop all services | `./manage-services.sh stop` |
| `restart` | Restart all services | `./manage-services.sh restart` |
| `kill [service]` | Stop specific service | `./manage-services.sh kill consul` |
| `refresh` | Refresh configurations | `./manage-services.sh refresh` |
| `open` | Open service interfaces | `./manage-services.sh open` |

## 🏃 Quick Workflows

### 🌅 Start Your Day
```bash
# Check what's running
./manage-services.sh status

# Start everything
./manage-services.sh start
```

### 🔧 Development Work
```bash
# Make configuration changes, then:
./manage-services.sh refresh

# Restart a specific service for debugging:
./manage-services.sh kill order
cd order-service && mvn spring-boot:run
```

### 🌙 End Your Day
```bash
# Stop everything
./manage-services.sh stop
```

## 🎯 Service Ports & URLs

| Service | Port | URL |
|---------|------|-----|
| **Consul UI** | 8500 | http://localhost:8500/ui |
| **Config Service** | 8888 | http://localhost:8888 |
| **Customer Service** | 8081 | http://localhost:8081 |
| **Inventory Service** | 8082 | http://localhost:8082 |
| **Order Service** | 8083 | http://localhost:8083 |
| **Gateway Service** | 9999 | http://localhost:9999 |

## 🛠️ Individual Scripts (if needed)

| Script | Purpose |
|--------|---------|
| `status-services.sh` | Detailed status check |
| `start-and-open-services.sh` | Complete startup |
| `kill-all-services.sh` | Stop all services |
| `kill-service.sh [service]` | Stop specific service |
| `refresh-all-services.sh` | Refresh configurations |
| `open-services.sh` | Open interfaces (with health check) |
| `quick-open-services.sh` | Quick open (no health check) |

## 🚨 Troubleshooting

### Services won't start?
```bash
# Check what's running
./manage-services.sh status

# Kill everything and restart
./manage-services.sh stop
./manage-services.sh start
```

### Port conflicts?
```bash
# Kill specific service
./manage-services.sh kill 8081

# Or kill all
./manage-services.sh stop
```

### Configuration issues?
```bash
# Refresh all configurations
./manage-services.sh refresh
```

## 💡 Pro Tips

1. **Always check status first**: `./manage-services.sh status`
2. **Use the master script**: It handles error checking and provides better feedback
3. **Service names**: Use `consul`, `config`, `customer`, `inventory`, `order`, `gateway`
4. **Port numbers**: You can also use port numbers like `8081`, `8500`, etc.
5. **Quick access**: Bookmark the service URLs for easy access

## 🎉 That's it!

You now have complete control over your microservices architecture with simple, memorable commands!
