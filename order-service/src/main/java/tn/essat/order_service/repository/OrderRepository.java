package tn.essat.order_service.repository;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;

import tn.essat.order_service.entity.Order;
import tn.essat.order_service.enums.OrderStatus;

/**
 * Repository pour la gestion des commandes
 * Inclut les projections et méthodes de recherche personnalisées
 */
@RepositoryRestResource(excerptProjection = tn.essat.order_service.entity.OrderListProjection.class)
public interface OrderRepository extends JpaRepository<Order, Long> {

    /**
     * Trouve les commandes par statut
     */
    List<Order> findByStatus(OrderStatus status);

    /**
     * Trouve les commandes par client
     */
    List<Order> findByCustomerId(Long customerId);

    /**
     * Trouve les commandes par statut et client
     */
    List<Order> findByStatusAndCustomerId(OrderStatus status, Long customerId);

    /**
     * Trouve les commandes dont le statut n'est pas dans la liste donnée
     */
    List<Order> findByStatusNotIn(List<OrderStatus> statuses);

    /**
     * Trouve les commandes créées à une date donnée
     */
    List<Order> findByCreatedAt(LocalDate createdAt);

    /**
     * Trouve les commandes créées entre deux dates
     */
    List<Order> findByCreatedAtBetween(LocalDate startDate, LocalDate endDate);

    /**
     * Trouve les commandes créées après une date donnée
     */
    List<Order> findByCreatedAtAfter(LocalDate date);

    /**
     * Trouve les commandes créées avant une date donnée
     */
    List<Order> findByCreatedAtBefore(LocalDate date);

    /**
     * Trouve les commandes par client et statut, triées par date de création
     */
    List<Order> findByCustomerIdAndStatusOrderByCreatedAtDesc(Long customerId, OrderStatus status);

    /**
     * Trouve toutes les commandes triées par date de création (plus récentes en premier)
     */
    List<Order> findAllByOrderByCreatedAtDesc();

    /**
     * Compte les commandes par statut
     */
    long countByStatus(OrderStatus status);

    /**
     * Compte les commandes par client
     */
    long countByCustomerId(Long customerId);

    /**
     * Requête personnalisée pour calculer le montant total des commandes livrées
     */
    @Query("SELECT SUM(pi.price * pi.quantity - pi.discount) FROM Order o " +
           "JOIN o.productItems pi WHERE o.status = :status")
    Double calculateTotalRevenueByStatus(@Param("status") OrderStatus status);

    /**
     * Requête personnalisée pour trouver les commandes avec un montant minimum
     */
    @Query("SELECT o FROM Order o WHERE " +
           "(SELECT SUM(pi.price * pi.quantity - pi.discount) FROM ProductItem pi WHERE pi.order = o) >= :minAmount")
    List<Order> findOrdersWithMinimumAmount(@Param("minAmount") Double minAmount);

    /**
     * Requête personnalisée pour trouver les commandes d'un client avec un statut spécifique
     * et incluant les détails des articles
     */
    @Query("SELECT DISTINCT o FROM Order o " +
           "LEFT JOIN FETCH o.productItems pi " +
           "WHERE o.customerId = :customerId AND o.status = :status")
    List<Order> findByCustomerIdAndStatusWithItems(@Param("customerId") Long customerId,
                                                   @Param("status") OrderStatus status);
}
