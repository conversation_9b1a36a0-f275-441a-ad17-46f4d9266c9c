package tn.essat.order_service.entity;

import java.time.LocalDate;

import org.springframework.data.rest.core.config.Projection;

import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;

/**
 * Projection légère pour l'entité Order
 * Utilisée pour les listes de commandes où on n'a pas besoin
 * de tous les détails des articles
 */
@Projection(name = "orderList", types = Order.class)
public interface OrderListProjection {

    /**
     * ID unique de la commande
     */
    Long getId();

    /**
     * Date de création de la commande
     */
    LocalDate getCreatedAt();

    /**
     * Statut actuel de la commande
     */
    OrderStatus getStatus();

    /**
     * ID du client qui a passé la commande
     */
    Long getCustomerId();

    /**
     * Informations de base du client
     */
    Customer getCustomer();

    /**
     * Nombre d'articles dans la commande
     * (sans charger tous les détails)
     */
    default int getItemCount() {
        return getProductItems() != null ? getProductItems().size() : 0;
    }

    /**
     * Montant total approximatif
     * (calculé à partir des articles si disponibles)
     */
    default double getEstimatedTotal() {
        if (getProductItems() == null) {
            return 0.0;
        }
        
        return getProductItems().stream()
            .mapToDouble(item -> {
                double baseAmount = item.getPrice() * item.getQuantity();
                return baseAmount - item.getDiscount();
            })
            .sum();
    }

    /**
     * Statut en texte lisible
     */
    default String getStatusText() {
        if (getStatus() == null) {
            return "Inconnu";
        }
        
        switch (getStatus()) {
            case CREATED:
                return "Créée";
            case PENDING:
                return "En attente";
            case CONFIRMED:
                return "Confirmée";
            case SHIPPED:
                return "Expédiée";
            case DELIVERED:
                return "Livrée";
            case CANCELED:
                return "Annulée";
            default:
                return getStatus().toString();
        }
    }

    /**
     * Nom du client (raccourci)
     */
    default String getCustomerName() {
        return getCustomer() != null ? getCustomer().getName() : "Client inconnu";
    }

    /**
     * Indique si la commande est active (non annulée/livrée)
     */
    default boolean isActive() {
        return getStatus() != OrderStatus.CANCELED && getStatus() != OrderStatus.DELIVERED;
    }

    /**
     * Référence interne pour les ProductItems
     * (utilisée pour les calculs mais pas exposée directement)
     */
    java.util.List<ProductItem> getProductItems();
}
