package tn.essat.order_service.entity;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.rest.core.config.Projection;

import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;

/**
 * Projection étendue pour l'entité Order avec calculs dérivés
 * Inclut toutes les informations de base plus des calculs utiles :
 * - Montant total de la commande
 * - Nombre d'articles
 * - Remises totales
 * - Informations de résumé
 */
@Projection(name = "orderSummary", types = Order.class)
public interface OrderSummaryProjection {

    /**
     * ID unique de la commande
     */
    Long getId();

    /**
     * Date de création de la commande
     */
    LocalDate getCreatedAt();

    /**
     * Statut actuel de la commande
     */
    OrderStatus getStatus();

    /**
     * ID du client qui a passé la commande
     */
    Long getCustomerId();

    /**
     * Informations complètes du client
     */
    Customer getCustomer();

    /**
     * Liste des articles de la commande
     */
    List<ProductItem> getProductItems();

    /**
     * Calcul du montant total de la commande
     * Somme de tous les sous-totaux des articles
     */
    default double getTotalAmount() {
        if (getProductItems() == null) {
            return 0.0;
        }
        
        return getProductItems().stream()
            .mapToDouble(item -> {
                double baseAmount = item.getPrice() * item.getQuantity();
                return baseAmount - item.getDiscount();
            })
            .sum();
    }

    /**
     * Calcul du montant total avant remises
     */
    default double getTotalBeforeDiscount() {
        if (getProductItems() == null) {
            return 0.0;
        }
        
        return getProductItems().stream()
            .mapToDouble(item -> item.getPrice() * item.getQuantity())
            .sum();
    }

    /**
     * Calcul du montant total des remises
     */
    default double getTotalDiscount() {
        return getTotalBeforeDiscount() - getTotalAmount();
    }

    /**
     * Nombre total d'articles dans la commande
     */
    default int getTotalItems() {
        if (getProductItems() == null) {
            return 0;
        }
        
        return getProductItems().stream()
            .mapToInt(ProductItem::getQuantity)
            .sum();
    }

    /**
     * Nombre de types de produits différents
     */
    default int getUniqueProductCount() {
        if (getProductItems() == null) {
            return 0;
        }
        
        return getProductItems().size();
    }

    /**
     * Pourcentage de remise moyen sur la commande
     */
    default double getAverageDiscountPercentage() {
        double totalBefore = getTotalBeforeDiscount();
        if (totalBefore > 0) {
            return (getTotalDiscount() / totalBefore) * 100;
        }
        return 0.0;
    }

    /**
     * Prix moyen par article
     */
    default double getAverageItemPrice() {
        int totalItems = getTotalItems();
        if (totalItems > 0) {
            return getTotalAmount() / totalItems;
        }
        return 0.0;
    }

    /**
     * Statut de la commande en texte lisible
     */
    default String getStatusDescription() {
        if (getStatus() == null) {
            return "Unknown";
        }
        
        switch (getStatus()) {
            case CREATED:
                return "Créée";
            case PENDING:
                return "En attente";
            case CONFIRMED:
                return "Confirmée";
            case SHIPPED:
                return "Expédiée";
            case DELIVERED:
                return "Livrée";
            case CANCELED:
                return "Annulée";
            default:
                return getStatus().toString();
        }
    }

    /**
     * Indique si la commande peut être modifiée
     */
    default boolean isModifiable() {
        return getStatus() == OrderStatus.CREATED || getStatus() == OrderStatus.PENDING;
    }

    /**
     * Indique si la commande peut être annulée
     */
    default boolean isCancellable() {
        return getStatus() == OrderStatus.CREATED ||
               getStatus() == OrderStatus.PENDING ||
               getStatus() == OrderStatus.CONFIRMED;
    }
}
