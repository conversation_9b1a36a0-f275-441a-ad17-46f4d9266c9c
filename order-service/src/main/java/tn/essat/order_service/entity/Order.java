package tn.essat.order_service.entity;

import java.time.LocalDate;
import java.util.List;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;



@Entity
@Table(name = "orders")
@Data @AllArgsConstructor @NoArgsConstructor @Builder

public class Order {

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private LocalDate createdAt;

    private OrderStatus status;

    private Long customerId;

    @Transient
    private Customer customer;

    @OneToMany(mappedBy = "orders")
    private List<ProductItem> productItems;

    
}
