package tn.essat.order_service.entity;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.rest.core.config.Projection;

import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;

/**
 * Projection complète pour l'entité Order
 * Expose toutes les informations d'une commande incluant :
 * - Informations de base de la commande
 * - Informations du client (via le service Customer)
 * - Liste des articles de la commande avec détails des produits
 */
@Projection(name = "fullOrder", types = Order.class)
public interface OrderProjection {

    /**
     * ID unique de la commande
     */
    Long getId();

    /**
     * Date de création de la commande
     */
    LocalDate getCreatedAt();

    /**
     * Statut actuel de la commande
     */
    OrderStatus getStatus();

    /**
     * ID du client qui a passé la commande
     */
    Long getCustomerId();

    /**
     * Informations complètes du client
     * (récupérées via le service Customer)
     */
    Customer getCustomer();

    /**
     * Liste des articles de la commande
     * Chaque article contient les informations du produit
     */
    List<ProductItem> getProductItems();

}

