package tn.essat.order_service.entity;

import org.springframework.data.rest.core.config.Projection;

import tn.essat.order_service.model.Product;

/**
 * Projection complète pour l'entité ProductItem
 * Expose toutes les informations d'un article de commande incluant :
 * - Informations de base de l'article
 * - Informations complètes du produit (via le service Inventory)
 * - Calculs dérivés (sous-total, etc.)
 */
@Projection(name = "fullProductItem", types = ProductItem.class)
public interface ProductItemProjection {

    /**
     * ID unique de l'article
     */
    Long getId();

    /**
     * ID du produit référencé
     */
    Long getProductId();

    /**
     * Informations complètes du produit
     * (récupérées via le service Inventory)
     */
    Product getProduct();

    /**
     * Prix unitaire de l'article au moment de la commande
     */
    double getPrice();

    /**
     * Quantité commandée
     */
    int getQuantity();

    /**
     * Remise appliquée (en pourcentage ou montant)
     */
    double getDiscount();

    /**
     * Calcul du sous-total de l'article
     * (prix * quantité - remise)
     * 
     * Note: Cette méthode peut être implémentée comme une méthode par défaut
     * ou calculée côté service
     */
    default double getSubTotal() {
        double baseAmount = getPrice() * getQuantity();
        return baseAmount - getDiscount();
    }

    /**
     * Calcul du montant de la remise
     * Si discount est un pourcentage (< 1), calcule le montant
     * Sinon retourne la valeur absolue
     */
    default double getDiscountAmount() {
        double baseAmount = getPrice() * getQuantity();
        if (getDiscount() < 1.0 && getDiscount() > 0) {
            // Discount est un pourcentage
            return baseAmount * getDiscount();
        } else {
            // Discount est un montant fixe
            return getDiscount();
        }
    }

    /**
     * Calcul du prix unitaire après remise
     */
    default double getEffectiveUnitPrice() {
        if (getQuantity() > 0) {
            return getSubTotal() / getQuantity();
        }
        return getPrice();
    }
}
