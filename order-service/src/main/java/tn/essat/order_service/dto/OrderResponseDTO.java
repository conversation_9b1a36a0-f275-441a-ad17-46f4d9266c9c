package tn.essat.order_service.dto;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;

/**
 * DTO pour les réponses API des commandes
 * Combine les informations des projections pour une réponse complète
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderResponseDTO {

    /**
     * Informations de base de la commande
     */
    private Long id;
    private LocalDate createdAt;
    private OrderStatus status;
    private String statusText;
    private Long customerId;

    /**
     * Informations du client
     */
    private Customer customer;

    /**
     * Articles de la commande
     */
    private List<ProductItemDTO> productItems;

    /**
     * Calculs et résumés
     */
    private OrderSummary summary;

    /**
     * Indicateurs d'état
     */
    private OrderFlags flags;

    /**
     * Classe interne pour les résumés de commande
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderSummary {
        private double totalAmount;
        private double totalBeforeDiscount;
        private double totalDiscount;
        private double averageDiscountPercentage;
        private int totalItems;
        private int uniqueProductCount;
        private double averageItemPrice;
    }

    /**
     * Classe interne pour les indicateurs d'état
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderFlags {
        private boolean isModifiable;
        private boolean isCancellable;
        private boolean isActive;
        private boolean hasDiscount;
    }

    /**
     * Méthode utilitaire pour créer un OrderResponseDTO à partir d'une entité Order
     */
    public static OrderResponseDTO fromOrder(tn.essat.order_service.entity.Order order) {
        if (order == null) {
            return null;
        }

        // Calculs des résumés
        double totalAmount = 0.0;
        double totalBeforeDiscount = 0.0;
        int totalItems = 0;

        List<ProductItemDTO> itemDTOs = null;
        if (order.getProductItems() != null) {
            itemDTOs = order.getProductItems().stream()
                .map(ProductItemDTO::fromProductItem)
                .toList();

            totalAmount = order.getProductItems().stream()
                .mapToDouble(item -> {
                    double baseAmount = item.getPrice() * item.getQuantity();
                    return baseAmount - item.getDiscount();
                })
                .sum();

            totalBeforeDiscount = order.getProductItems().stream()
                .mapToDouble(item -> item.getPrice() * item.getQuantity())
                .sum();

            totalItems = order.getProductItems().stream()
                .mapToInt(item -> item.getQuantity())
                .sum();
        }

        double totalDiscount = totalBeforeDiscount - totalAmount;
        double averageDiscountPercentage = totalBeforeDiscount > 0 ? 
            (totalDiscount / totalBeforeDiscount) * 100 : 0.0;

        // Construction du DTO
        return OrderResponseDTO.builder()
            .id(order.getId())
            .createdAt(order.getCreatedAt())
            .status(order.getStatus())
            .statusText(getStatusText(order.getStatus()))
            .customerId(order.getCustomerId())
            .customer(order.getCustomer())
            .productItems(itemDTOs)
            .summary(OrderSummary.builder()
                .totalAmount(totalAmount)
                .totalBeforeDiscount(totalBeforeDiscount)
                .totalDiscount(totalDiscount)
                .averageDiscountPercentage(averageDiscountPercentage)
                .totalItems(totalItems)
                .uniqueProductCount(order.getProductItems() != null ? order.getProductItems().size() : 0)
                .averageItemPrice(totalItems > 0 ? totalAmount / totalItems : 0.0)
                .build())
            .flags(OrderFlags.builder()
                .isModifiable(isModifiable(order.getStatus()))
                .isCancellable(isCancellable(order.getStatus()))
                .isActive(isActive(order.getStatus()))
                .hasDiscount(totalDiscount > 0)
                .build())
            .build();
    }

    /**
     * Méthodes utilitaires pour les calculs d'état
     */
    private static String getStatusText(OrderStatus status) {
        if (status == null) return "Inconnu";
        
        switch (status) {
            case CREATED: return "Créée";
            case PENDING: return "En attente";
            case CONFIRMED: return "Confirmée";
            case SHIPPED: return "Expédiée";
            case DELIVERED: return "Livrée";
            case CANCELED: return "Annulée";
            default: return status.toString();
        }
    }

    private static boolean isModifiable(OrderStatus status) {
        return status == OrderStatus.CREATED || status == OrderStatus.PENDING;
    }

    private static boolean isCancellable(OrderStatus status) {
        return status == OrderStatus.CREATED || 
               status == OrderStatus.PENDING || 
               status == OrderStatus.CONFIRMED;
    }

    private static boolean isActive(OrderStatus status) {
        return status != OrderStatus.CANCELED && status != OrderStatus.DELIVERED;
    }
}
