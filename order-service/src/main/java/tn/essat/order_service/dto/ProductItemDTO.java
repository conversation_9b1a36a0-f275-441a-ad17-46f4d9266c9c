package tn.essat.order_service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tn.essat.order_service.entity.ProductItem;
import tn.essat.order_service.model.Product;

/**
 * DTO pour les articles de commande
 * Inclut toutes les informations d'un article avec calculs dérivés
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductItemDTO {

    /**
     * Informations de base de l'article
     */
    private Long id;
    private Long productId;
    private double price;
    private int quantity;
    private double discount;

    /**
     * Informations du produit
     */
    private Product product;

    /**
     * Calculs dérivés
     */
    private ProductItemCalculations calculations;

    /**
     * Classe interne pour les calculs d'article
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductItemCalculations {
        private double subTotal;
        private double discountAmount;
        private double effectiveUnitPrice;
        private boolean hasDiscount;
        private double discountPercentage;
    }

    /**
     * Méthode utilitaire pour créer un ProductItemDTO à partir d'une entité ProductItem
     */
    public static ProductItemDTO fromProductItem(ProductItem item) {
        if (item == null) {
            return null;
        }

        // Calculs
        double baseAmount = item.getPrice() * item.getQuantity();
        double discountAmount = item.getDiscount();
        double subTotal = baseAmount - discountAmount;
        double effectiveUnitPrice = item.getQuantity() > 0 ? subTotal / item.getQuantity() : item.getPrice();
        boolean hasDiscount = discountAmount > 0;
        double discountPercentage = baseAmount > 0 ? (discountAmount / baseAmount) * 100 : 0.0;

        return ProductItemDTO.builder()
            .id(item.getId())
            .productId(item.getProductId())
            .price(item.getPrice())
            .quantity(item.getQuantity())
            .discount(item.getDiscount())
            .product(item.getProduct())
            .calculations(ProductItemCalculations.builder()
                .subTotal(subTotal)
                .discountAmount(discountAmount)
                .effectiveUnitPrice(effectiveUnitPrice)
                .hasDiscount(hasDiscount)
                .discountPercentage(discountPercentage)
                .build())
            .build();
    }

    /**
     * Méthodes utilitaires pour accès direct aux calculs
     */
    public double getSubTotal() {
        return calculations != null ? calculations.getSubTotal() : 
               (price * quantity) - discount;
    }

    public double getDiscountAmount() {
        return calculations != null ? calculations.getDiscountAmount() : discount;
    }

    public double getEffectiveUnitPrice() {
        return calculations != null ? calculations.getEffectiveUnitPrice() : 
               (quantity > 0 ? getSubTotal() / quantity : price);
    }

    public boolean hasDiscount() {
        return calculations != null ? calculations.isHasDiscount() : discount > 0;
    }

    public double getDiscountPercentage() {
        return calculations != null ? calculations.getDiscountPercentage() : 
               (price * quantity > 0 ? (discount / (price * quantity)) * 100 : 0.0);
    }

    /**
     * Nom du produit (raccourci)
     */
    public String getProductName() {
        return product != null ? product.getName() : "Produit inconnu";
    }

    /**
     * Vérifie si l'article est valide
     */
    public boolean isValid() {
        return productId != null && 
               price >= 0 && 
               quantity > 0 && 
               discount >= 0 && 
               discount <= (price * quantity);
    }

    /**
     * Calcule l'économie réalisée
     */
    public double getSavings() {
        return getDiscountAmount();
    }

    /**
     * Retourne le prix unitaire original du produit
     */
    public double getOriginalUnitPrice() {
        return price;
    }

    /**
     * Retourne le montant total avant remise
     */
    public double getTotalBeforeDiscount() {
        return price * quantity;
    }
}
