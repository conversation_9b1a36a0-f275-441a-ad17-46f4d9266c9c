package tn.essat.order_service.services;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.hateoas.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import tn.essat.order_service.model.Customer;




@FeignClient(name = "customer-service") 

public interface CustomerRestClientService {

    @GetMapping("/customers/{id}?projection=fullCustomer")
    public Customer getCustomerById(@PathVariable("id") Long id);

    
    @GetMapping("/customers?projection=fullCustomer")
    public PagedModel<Customer> getAllCustomers();

}


