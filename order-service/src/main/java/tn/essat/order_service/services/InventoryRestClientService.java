package tn.essat.order_service.services;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.hateoas.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import tn.essat.order_service.model.Product;




@FeignClient(name = "inventory-service") 

public interface InventoryRestClientService {

    @GetMapping("/products/{id}?projection=fullProduct")
    public Product getProductById(@PathVariable("id") Long id);

    
    @GetMapping("/products?projection=fullProduct")
    public PagedModel<Product> getAllProducts();
 
}


