package tn.essat.order_service;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Random;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

import tn.essat.order_service.entity.Order;
import tn.essat.order_service.entity.ProductItem;
import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;
import tn.essat.order_service.model.Product;
import tn.essat.order_service.repository.OrderRepository;
import tn.essat.order_service.repository.ProductItemRepository;
import tn.essat.order_service.services.CustomerRestClientService;
import tn.essat.order_service.services.InventoryRestClientService;




@SpringBootApplication
@EnableFeignClients

public class OrderServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(OrderServiceApplication.class, args);
	}



	@Bean

	CommandLineRunner start(
			OrderRepository orderRepository, 
			ProductItemRepository productItemRepository,
			CustomerRestClientService customerRestClientService,
			InventoryRestClientService inventoryRestClientService) {

		return args -> {

			List<Customer> customers = customerRestClientService.getAllCustomers().getContent().stream().toList();

			List<Product> products = inventoryRestClientService.getAllProducts().getContent().stream().toList();

			Long customerId = 1L;

			Random random = new Random();

			Customer customer = customerRestClientService.getCustomerById(customerId);

			for (int i=1; i <=20; i++) {

				Order order = Order.builder()
						.customerId(customers.get(random.nextInt(customers.size())).getId())
						.status(Math.random()>0.5 ? OrderStatus.PENDING : OrderStatus.CREATED)
						.createdAt(LocalDate.now())
						.build();

				Order savedOrder = orderRepository.save(order);

				for (int j=1; j < products.size(); j++) {

					if (Math.random() > 0.70) {
						
						ProductItem productItem = ProductItem.builder()
								.order(savedOrder)
								.productId(products.get(j).getId())
								.price(products.get(j).getPrice())
								.quantity(1+random.nextInt(10))
								.discount(Math.round(Math.random()*100)/100)
								.build();
						
						productItemRepository.save(productItem);
					}

				}
			}

		};
	}


}
