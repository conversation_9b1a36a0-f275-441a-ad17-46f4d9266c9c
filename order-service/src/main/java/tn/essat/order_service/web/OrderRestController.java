package tn.essat.order_service.web;


import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import tn.essat.order_service.model.Customer;
import tn.essat.order_service.model.Product;

import tn.essat.order_service.entity.Order;
import tn.essat.order_service.repository.OrderRepository;
import tn.essat.order_service.repository.ProductItemRepository;
import tn.essat.order_service.services.CustomerRestClientService;
import tn.essat.order_service.services.InventoryRestClientService;




@RestController

public class OrderRestController {

    
    private OrderRepository orderRepository;

    private ProductItemRepository productItemRepository;

    private CustomerRestClientService customerRestClientService;

    private InventoryRestClientService inventoryRestClientService;



    public OrderRestController(OrderRepository orderRepository, ProductItemRepository productItemRepository,
                                CustomerRestClientService customerRestClientService,
                                InventoryRestClientService inventoryRestClientService) {

        this.orderRepository = orderRepository;
        this.productItemRepository = productItemRepository;
        this.customerRestClientService = customerRestClientService;
        this.inventoryRestClientService = inventoryRestClientService;
    }


    
    @GetMapping("/fullOrder/{id}")

    public Order getOrder(@PathVariable Long id) {

        Order order = orderRepository.findById(id).get();

        Customer customer = customerRestClientService.getCustomerById(order.getCustomerId());

        order.setCustomer(customer);

        order.getProductItems().forEach(pi -> {

            Product product = inventoryRestClientService.getProductById(pi.getProductId());

            pi.setProduct(product);

        });

        return order; 

    } 

}


