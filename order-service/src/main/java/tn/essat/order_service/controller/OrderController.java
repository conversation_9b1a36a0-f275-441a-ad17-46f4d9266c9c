package tn.essat.order_service.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import tn.essat.order_service.dto.OrderResponseDTO;
import tn.essat.order_service.entity.Order;
import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.repository.OrderRepository;

/**
 * Contrôleur REST pour la gestion des commandes
 * Utilise les projections et DTOs pour des réponses optimisées
 */
@RestController
@RequestMapping("/api/orders")
public class OrderController {

    @Autowired
    private OrderRepository orderRepository;

    /**
     * Récupère toutes les commandes avec projection légère
     */
    @GetMapping
    public ResponseEntity<List<OrderResponseDTO>> getAllOrders(
            @RequestParam(required = false) OrderStatus status,
            @RequestParam(required = false) Long customerId) {
        
        List<Order> orders;
        
        if (status != null && customerId != null) {
            orders = orderRepository.findByStatusAndCustomerId(status, customerId);
        } else if (status != null) {
            orders = orderRepository.findByStatus(status);
        } else if (customerId != null) {
            orders = orderRepository.findByCustomerId(customerId);
        } else {
            orders = orderRepository.findAll();
        }

        List<OrderResponseDTO> orderDTOs = orders.stream()
            .map(OrderResponseDTO::fromOrder)
            .toList();

        return ResponseEntity.ok(orderDTOs);
    }

    /**
     * Récupère une commande spécifique avec tous les détails
     */
    @GetMapping("/{id}")
    public ResponseEntity<OrderResponseDTO> getOrderById(@PathVariable Long id) {
        Optional<Order> orderOpt = orderRepository.findById(id);
        
        if (orderOpt.isPresent()) {
            OrderResponseDTO orderDTO = OrderResponseDTO.fromOrder(orderOpt.get());
            return ResponseEntity.ok(orderDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Récupère les commandes d'un client spécifique
     */
    @GetMapping("/customer/{customerId}")
    public ResponseEntity<List<OrderResponseDTO>> getOrdersByCustomer(@PathVariable Long customerId) {
        List<Order> orders = orderRepository.findByCustomerId(customerId);
        
        List<OrderResponseDTO> orderDTOs = orders.stream()
            .map(OrderResponseDTO::fromOrder)
            .toList();

        return ResponseEntity.ok(orderDTOs);
    }

    /**
     * Récupère les commandes par statut
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<OrderResponseDTO>> getOrdersByStatus(@PathVariable OrderStatus status) {
        List<Order> orders = orderRepository.findByStatus(status);
        
        List<OrderResponseDTO> orderDTOs = orders.stream()
            .map(OrderResponseDTO::fromOrder)
            .toList();

        return ResponseEntity.ok(orderDTOs);
    }

    /**
     * Récupère les commandes actives (non annulées/livrées)
     */
    @GetMapping("/active")
    public ResponseEntity<List<OrderResponseDTO>> getActiveOrders() {
        List<Order> orders = orderRepository.findByStatusNotIn(
            List.of(OrderStatus.CANCELED, OrderStatus.DELIVERED)
        );
        
        List<OrderResponseDTO> orderDTOs = orders.stream()
            .map(OrderResponseDTO::fromOrder)
            .toList();

        return ResponseEntity.ok(orderDTOs);
    }

    /**
     * Récupère un résumé des statistiques des commandes
     */
    @GetMapping("/stats")
    public ResponseEntity<OrderStats> getOrderStats() {
        List<Order> allOrders = orderRepository.findAll();
        
        long totalOrders = allOrders.size();
        long activeOrders = allOrders.stream()
            .filter(order -> order.getStatus() != OrderStatus.CANCELED && 
                           order.getStatus() != OrderStatus.DELIVERED)
            .count();
        
        double totalRevenue = allOrders.stream()
            .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
            .mapToDouble(order -> {
                if (order.getProductItems() != null) {
                    return order.getProductItems().stream()
                        .mapToDouble(item -> (item.getPrice() * item.getQuantity()) - item.getDiscount())
                        .sum();
                }
                return 0.0;
            })
            .sum();

        OrderStats stats = OrderStats.builder()
            .totalOrders(totalOrders)
            .activeOrders(activeOrders)
            .completedOrders(allOrders.stream()
                .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
                .count())
            .canceledOrders(allOrders.stream()
                .filter(order -> order.getStatus() == OrderStatus.CANCELED)
                .count())
            .totalRevenue(totalRevenue)
            .averageOrderValue(totalOrders > 0 ? totalRevenue / totalOrders : 0.0)
            .build();

        return ResponseEntity.ok(stats);
    }

    /**
     * Classe interne pour les statistiques des commandes
     */
    public static class OrderStats {
        public long totalOrders;
        public long activeOrders;
        public long completedOrders;
        public long canceledOrders;
        public double totalRevenue;
        public double averageOrderValue;

        public static OrderStatsBuilder builder() {
            return new OrderStatsBuilder();
        }

        public static class OrderStatsBuilder {
            private long totalOrders;
            private long activeOrders;
            private long completedOrders;
            private long canceledOrders;
            private double totalRevenue;
            private double averageOrderValue;

            public OrderStatsBuilder totalOrders(long totalOrders) {
                this.totalOrders = totalOrders;
                return this;
            }

            public OrderStatsBuilder activeOrders(long activeOrders) {
                this.activeOrders = activeOrders;
                return this;
            }

            public OrderStatsBuilder completedOrders(long completedOrders) {
                this.completedOrders = completedOrders;
                return this;
            }

            public OrderStatsBuilder canceledOrders(long canceledOrders) {
                this.canceledOrders = canceledOrders;
                return this;
            }

            public OrderStatsBuilder totalRevenue(double totalRevenue) {
                this.totalRevenue = totalRevenue;
                return this;
            }

            public OrderStatsBuilder averageOrderValue(double averageOrderValue) {
                this.averageOrderValue = averageOrderValue;
                return this;
            }

            public OrderStats build() {
                OrderStats stats = new OrderStats();
                stats.totalOrders = this.totalOrders;
                stats.activeOrders = this.activeOrders;
                stats.completedOrders = this.completedOrders;
                stats.canceledOrders = this.canceledOrders;
                stats.totalRevenue = this.totalRevenue;
                stats.averageOrderValue = this.averageOrderValue;
                return stats;
            }
        }
    }
}
