package tn.essat.order_service.projection;

import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import tn.essat.order_service.dto.OrderResponseDTO;
import tn.essat.order_service.dto.ProductItemDTO;
import tn.essat.order_service.entity.Order;
import tn.essat.order_service.entity.ProductItem;
import tn.essat.order_service.enums.OrderStatus;
import tn.essat.order_service.model.Customer;
import tn.essat.order_service.model.Product;

/**
 * Tests unitaires pour les projections et DTOs des commandes
 */
@SpringBootTest
public class OrderProjectionTest {

    private Order testOrder;
    private Customer testCustomer;
    private Product testProduct1;
    private Product testProduct2;
    private ProductItem testItem1;
    private ProductItem testItem2;

    @BeforeEach
    void setUp() {
        // Création du client de test
        testCustomer = new Customer();
        testCustomer.setId(1L);
        testCustomer.setName("John Doe");
        testCustomer.setEmail("<EMAIL>");

        // Création des produits de test
        testProduct1 = new Product();
        testProduct1.setId(101L);
        testProduct1.setName("Laptop");
        testProduct1.setPrice(1000.0);
        testProduct1.setQuantity(50);

        testProduct2 = new Product();
        testProduct2.setId(102L);
        testProduct2.setName("Mouse");
        testProduct2.setPrice(25.0);
        testProduct2.setQuantity(100);

        // Création des articles de commande
        testItem1 = ProductItem.builder()
            .id(1L)
            .productId(101L)
            .product(testProduct1)
            .price(1000.0)
            .quantity(1)
            .discount(100.0) // 100€ de remise
            .build();

        testItem2 = ProductItem.builder()
            .id(2L)
            .productId(102L)
            .product(testProduct2)
            .price(25.0)
            .quantity(2)
            .discount(5.0) // 5€ de remise
            .build();

        // Création de la commande de test
        testOrder = Order.builder()
            .id(1L)
            .createdAt(LocalDate.now())
            .status(OrderStatus.PENDING)
            .customerId(1L)
            .customer(testCustomer)
            .productItems(Arrays.asList(testItem1, testItem2))
            .build();

        // Association bidirectionnelle
        testItem1.setOrder(testOrder);
        testItem2.setOrder(testOrder);
    }

    @Test
    void testProductItemDTO_Calculations() {
        // Test des calculs pour l'article 1
        ProductItemDTO dto1 = ProductItemDTO.fromProductItem(testItem1);
        
        assertNotNull(dto1);
        assertEquals(1L, dto1.getId());
        assertEquals(101L, dto1.getProductId());
        assertEquals(1000.0, dto1.getPrice());
        assertEquals(1, dto1.getQuantity());
        assertEquals(100.0, dto1.getDiscount());
        
        // Vérification des calculs
        assertEquals(900.0, dto1.getSubTotal()); // 1000 * 1 - 100
        assertEquals(100.0, dto1.getDiscountAmount());
        assertEquals(900.0, dto1.getEffectiveUnitPrice()); // 900 / 1
        assertTrue(dto1.hasDiscount());
        assertEquals(10.0, dto1.getDiscountPercentage(), 0.01); // 100/1000 * 100
        
        // Test des calculs pour l'article 2
        ProductItemDTO dto2 = ProductItemDTO.fromProductItem(testItem2);
        
        assertEquals(45.0, dto2.getSubTotal()); // 25 * 2 - 5
        assertEquals(5.0, dto2.getDiscountAmount());
        assertEquals(22.5, dto2.getEffectiveUnitPrice()); // 45 / 2
        assertTrue(dto2.hasDiscount());
        assertEquals(10.0, dto2.getDiscountPercentage(), 0.01); // 5/50 * 100
    }

    @Test
    void testOrderResponseDTO_Summary() {
        OrderResponseDTO dto = OrderResponseDTO.fromOrder(testOrder);
        
        assertNotNull(dto);
        assertEquals(1L, dto.getId());
        assertEquals(LocalDate.now(), dto.getCreatedAt());
        assertEquals(OrderStatus.PENDING, dto.getStatus());
        assertEquals("En attente", dto.getStatusText());
        assertEquals(1L, dto.getCustomerId());
        
        // Vérification du client
        assertNotNull(dto.getCustomer());
        assertEquals("John Doe", dto.getCustomer().getName());
        
        // Vérification des articles
        assertNotNull(dto.getProductItems());
        assertEquals(2, dto.getProductItems().size());
        
        // Vérification du résumé
        OrderResponseDTO.OrderSummary summary = dto.getSummary();
        assertNotNull(summary);
        
        // Total: (1000*1 - 100) + (25*2 - 5) = 900 + 45 = 945
        assertEquals(945.0, summary.getTotalAmount(), 0.01);
        
        // Total avant remise: (1000*1) + (25*2) = 1000 + 50 = 1050
        assertEquals(1050.0, summary.getTotalBeforeDiscount(), 0.01);
        
        // Total remise: 100 + 5 = 105
        assertEquals(105.0, summary.getTotalDiscount(), 0.01);
        
        // Pourcentage de remise: 105/1050 * 100 = 10%
        assertEquals(10.0, summary.getAverageDiscountPercentage(), 0.01);
        
        // Total articles: 1 + 2 = 3
        assertEquals(3, summary.getTotalItems());
        
        // Produits uniques: 2
        assertEquals(2, summary.getUniqueProductCount());
        
        // Prix moyen par article: 945 / 3 = 315
        assertEquals(315.0, summary.getAverageItemPrice(), 0.01);
    }

    @Test
    void testOrderResponseDTO_Flags() {
        OrderResponseDTO dto = OrderResponseDTO.fromOrder(testOrder);
        
        OrderResponseDTO.OrderFlags flags = dto.getFlags();
        assertNotNull(flags);
        
        // Commande PENDING doit être modifiable et annulable
        assertTrue(flags.isModifiable());
        assertTrue(flags.isCancellable());
        assertTrue(flags.isActive());
        assertTrue(flags.isHasDiscount()); // Il y a des remises
    }

    @Test
    void testOrderResponseDTO_DeliveredOrder() {
        // Test avec une commande livrée
        testOrder.setStatus(OrderStatus.DELIVERED);
        
        OrderResponseDTO dto = OrderResponseDTO.fromOrder(testOrder);
        
        assertEquals("Livrée", dto.getStatusText());
        
        OrderResponseDTO.OrderFlags flags = dto.getFlags();
        assertFalse(flags.isModifiable()); // Commande livrée non modifiable
        assertFalse(flags.isCancellable()); // Commande livrée non annulable
        assertFalse(flags.isActive()); // Commande livrée non active
    }

    @Test
    void testOrderResponseDTO_CanceledOrder() {
        // Test avec une commande annulée
        testOrder.setStatus(OrderStatus.CANCELED);
        
        OrderResponseDTO dto = OrderResponseDTO.fromOrder(testOrder);
        
        assertEquals("Annulée", dto.getStatusText());
        
        OrderResponseDTO.OrderFlags flags = dto.getFlags();
        assertFalse(flags.isModifiable());
        assertFalse(flags.isCancellable());
        assertFalse(flags.isActive());
    }

    @Test
    void testOrderResponseDTO_EmptyOrder() {
        // Test avec une commande sans articles
        Order emptyOrder = Order.builder()
            .id(2L)
            .createdAt(LocalDate.now())
            .status(OrderStatus.CREATED)
            .customerId(1L)
            .customer(testCustomer)
            .productItems(List.of()) // Liste vide
            .build();
        
        OrderResponseDTO dto = OrderResponseDTO.fromOrder(emptyOrder);
        
        assertNotNull(dto);
        assertEquals(0, dto.getProductItems().size());
        
        OrderResponseDTO.OrderSummary summary = dto.getSummary();
        assertEquals(0.0, summary.getTotalAmount());
        assertEquals(0.0, summary.getTotalBeforeDiscount());
        assertEquals(0.0, summary.getTotalDiscount());
        assertEquals(0, summary.getTotalItems());
        assertEquals(0, summary.getUniqueProductCount());
    }

    @Test
    void testProductItemDTO_Validation() {
        ProductItemDTO validDto = ProductItemDTO.fromProductItem(testItem1);
        assertTrue(validDto.isValid());
        
        // Test avec des données invalides
        ProductItem invalidItem = ProductItem.builder()
            .id(3L)
            .productId(null) // ID produit manquant
            .price(-10.0) // Prix négatif
            .quantity(0) // Quantité nulle
            .discount(2000.0) // Remise supérieure au total
            .build();
        
        ProductItemDTO invalidDto = ProductItemDTO.fromProductItem(invalidItem);
        assertFalse(invalidDto.isValid());
    }

    @Test
    void testProductItemDTO_UtilityMethods() {
        ProductItemDTO dto = ProductItemDTO.fromProductItem(testItem1);
        
        assertEquals("Laptop", dto.getProductName());
        assertEquals(100.0, dto.getSavings());
        assertEquals(1000.0, dto.getOriginalUnitPrice());
        assertEquals(1000.0, dto.getTotalBeforeDiscount());
    }
}
