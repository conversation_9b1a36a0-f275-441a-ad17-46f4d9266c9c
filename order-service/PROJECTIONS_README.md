# 📊 Order Service - Projections et DTOs

Ce document explique les différentes projections et DTOs disponibles dans le service Order pour optimiser les réponses API.

## 🎯 Vue d'ensemble

Le service Order propose plusieurs niveaux de détail pour les réponses API :

| Type | Usage | Performance | Détails |
|------|-------|-------------|---------|
| **OrderListProjection** | Listes de commandes | ⚡ Rapide | Informations de base |
| **OrderProjection** | Détails complets | 🐌 Lent | Toutes les informations |
| **OrderSummaryProjection** | Vue avec calculs | 🔄 Moyen | Informations + calculs |
| **OrderResponseDTO** | API personnalisée | 🔄 Moyen | Structure optimisée |

## 📋 Projections Disponibles

### 1. OrderListProjection (`orderList`)

**Usage :** Listes de commandes, tableaux de bord

**Contenu :**
- ✅ Informations de base de la commande
- ✅ Informations du client
- ✅ Nombre d'articles (sans détails)
- ✅ Montant total estimé
- ✅ Statut en texte lisible

**Exemple d'utilisation :**
```http
GET /orders?projection=orderList
```

### 2. OrderProjection (`fullOrder`)

**Usage :** Détails complets d'une commande

**Contenu :**
- ✅ Toutes les informations de la commande
- ✅ Informations complètes du client
- ✅ Liste complète des articles
- ✅ Détails des produits

**Exemple d'utilisation :**
```http
GET /orders/1?projection=fullOrder
```

### 3. OrderSummaryProjection (`orderSummary`)

**Usage :** Vue détaillée avec calculs automatiques

**Contenu :**
- ✅ Informations complètes de la commande
- ✅ Calculs dérivés (totaux, remises, moyennes)
- ✅ Indicateurs d'état (modifiable, annulable)
- ✅ Statistiques des articles

**Calculs inclus :**
- `getTotalAmount()` - Montant total après remises
- `getTotalBeforeDiscount()` - Montant avant remises
- `getTotalDiscount()` - Montant total des remises
- `getTotalItems()` - Nombre total d'articles
- `getAverageItemPrice()` - Prix moyen par article
- `isModifiable()` - Commande modifiable ?
- `isCancellable()` - Commande annulable ?

**Exemple d'utilisation :**
```http
GET /orders/1?projection=orderSummary
```

### 4. ProductItemProjection (`fullProductItem`)

**Usage :** Détails complets des articles de commande

**Contenu :**
- ✅ Informations de l'article
- ✅ Détails du produit
- ✅ Calculs dérivés (sous-total, prix effectif)

**Calculs inclus :**
- `getSubTotal()` - Sous-total de l'article
- `getDiscountAmount()` - Montant de la remise
- `getEffectiveUnitPrice()` - Prix unitaire après remise

## 🚀 API Personnalisée (DTOs)

### OrderResponseDTO

Structure optimisée pour les réponses API avec :

```json
{
  "id": 1,
  "createdAt": "2024-01-15",
  "status": "DELIVERED",
  "statusText": "Livrée",
  "customerId": 1,
  "customer": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "productItems": [...],
  "summary": {
    "totalAmount": 150.00,
    "totalBeforeDiscount": 180.00,
    "totalDiscount": 30.00,
    "averageDiscountPercentage": 16.67,
    "totalItems": 3,
    "uniqueProductCount": 2,
    "averageItemPrice": 50.00
  },
  "flags": {
    "isModifiable": false,
    "isCancellable": false,
    "isActive": false,
    "hasDiscount": true
  }
}
```

### ProductItemDTO

Structure détaillée pour les articles :

```json
{
  "id": 1,
  "productId": 101,
  "price": 50.00,
  "quantity": 2,
  "discount": 10.00,
  "product": {
    "id": 101,
    "name": "Produit A",
    "price": 50.00,
    "quantity": 100
  },
  "calculations": {
    "subTotal": 90.00,
    "discountAmount": 10.00,
    "effectiveUnitPrice": 45.00,
    "hasDiscount": true,
    "discountPercentage": 10.0
  }
}
```

## 🛠️ Endpoints Disponibles

### Spring Data REST (avec projections)

```http
# Liste avec projection légère (par défaut)
GET /orders

# Liste avec projection complète
GET /orders?projection=fullOrder

# Liste avec calculs
GET /orders?projection=orderSummary

# Détail d'une commande
GET /orders/1?projection=fullOrder
```

### API Personnalisée

```http
# Toutes les commandes
GET /api/orders

# Commandes par statut
GET /api/orders/status/PENDING

# Commandes par client
GET /api/orders/customer/1

# Commandes actives
GET /api/orders/active

# Statistiques
GET /api/orders/stats

# Avec filtres
GET /api/orders?status=DELIVERED&customerId=1
```

## 📈 Optimisation des Performances

### Recommandations d'usage :

1. **Listes** → `OrderListProjection` ou `/api/orders`
2. **Détails** → `OrderProjection` ou `/api/orders/{id}`
3. **Calculs** → `OrderSummaryProjection`
4. **Statistiques** → `/api/orders/stats`

### Éviter :

- ❌ `fullOrder` pour les listes (trop lourd)
- ❌ Charger tous les détails si non nécessaire
- ❌ Calculs côté client (utiliser les projections)

## 🔍 Exemples d'Utilisation

### Dashboard - Liste des commandes
```http
GET /orders?projection=orderList
```

### Détail d'une commande
```http
GET /orders/1?projection=fullOrder
```

### Rapport financier
```http
GET /orders?projection=orderSummary
GET /api/orders/stats
```

### Commandes d'un client
```http
GET /api/orders/customer/123
```

### Commandes en attente
```http
GET /api/orders/status/PENDING
```

## 🎯 Bonnes Pratiques

1. **Choisir la bonne projection** selon le besoin
2. **Utiliser l'API personnalisée** pour des cas complexes
3. **Éviter le sur-chargement** de données
4. **Mettre en cache** les résultats fréquents
5. **Paginer** les grandes listes

Cette architecture permet une flexibilité maximale tout en optimisant les performances selon le contexte d'utilisation.
