#!/bin/bash

# Script to kill a specific service by name or port

show_usage() {
    echo "🛑 Kill Specific Service"
    echo "======================="
    echo ""
    echo "Usage: $0 [service_name|port]"
    echo ""
    echo "Available services:"
    echo "  consul          - Consul (port 8500)"
    echo "  config          - Config Service (port 8888)"
    echo "  customer        - Customer Service (port 8081)"
    echo "  inventory       - Inventory Service (port 8082)"
    echo "  order           - Order Service (port 8083)"
    echo "  gateway         - Gateway Service (port 9999)"
    echo "  all             - All services"
    echo ""
    echo "Examples:"
    echo "  $0 consul       # Stop Consul"
    echo "  $0 8081         # Stop service on port 8081"
    echo "  $0 all          # Stop all services"
    echo ""
    echo "Current running services:"
    check_all_services
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to get process ID using a port
get_pid_by_port() {
    local port=$1
    lsof -ti :$port 2>/dev/null
}

# Function to kill process by PID
kill_process() {
    local pid=$1
    local service_name=$2
    
    if kill $pid 2>/dev/null; then
        echo "   ✅ Process $pid killed successfully"
        
        # Wait a moment and check if process is really dead
        sleep 2
        if kill -0 $pid 2>/dev/null; then
            echo "   ⚠️  Process still running, forcing kill..."
            kill -9 $pid 2>/dev/null
            sleep 1
            if kill -0 $pid 2>/dev/null; then
                echo "   ❌ Failed to kill process $pid"
                return 1
            else
                echo "   ✅ Process $pid force-killed successfully"
            fi
        fi
        return 0
    else
        echo "   ❌ Failed to kill process $pid"
        return 1
    fi
}

# Function to stop a service by port
stop_service_by_port() {
    local port=$1
    local service_name=$2
    
    echo "🔍 Checking $service_name (port $port)..."
    
    if check_port $port; then
        local pids=$(get_pid_by_port $port)
        if [ -n "$pids" ]; then
            echo "   📍 Found running processes: $pids"
            for pid in $pids; do
                echo "   🛑 Stopping process $pid..."
                if kill_process $pid "$service_name"; then
                    echo "   ✅ $service_name stopped successfully"
                    return 0
                else
                    echo "   ❌ Failed to stop $service_name (PID: $pid)"
                    return 1
                fi
            done
        else
            echo "   ⚠️  Port $port is in use but no PID found"
            return 1
        fi
    else
        echo "   ✅ $service_name is not running (port $port is free)"
        return 0
    fi
}

# Function to check all services status
check_all_services() {
    local services=(
        "Consul:8500"
        "Config Service:8888"
        "Customer Service:8081"
        "Inventory Service:8082"
        "Order Service:8083"
        "Gateway Service:9999"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if check_port $port; then
            local pids=$(get_pid_by_port $port)
            echo "  ✅ $name (port $port) - Running (PID: $pids)"
        else
            echo "  ❌ $name (port $port) - Stopped"
        fi
    done
}

# Main logic
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

service_input="$1"

case "$service_input" in
    "consul"|"8500")
        stop_service_by_port 8500 "Consul"
        ;;
    "config"|"8888")
        stop_service_by_port 8888 "Config Service"
        ;;
    "customer"|"8081")
        stop_service_by_port 8081 "Customer Service"
        ;;
    "inventory"|"8082")
        stop_service_by_port 8082 "Inventory Service"
        ;;
    "order"|"8083")
        stop_service_by_port 8083 "Order Service"
        ;;
    "gateway"|"9999")
        stop_service_by_port 9999 "Gateway Service"
        ;;
    "all")
        echo "🛑 Stopping all services..."
        ./kill-all-services.sh
        ;;
    *)
        # Check if it's a numeric port
        if [[ "$service_input" =~ ^[0-9]+$ ]]; then
            stop_service_by_port "$service_input" "Service on port $service_input"
        else
            echo "❌ Unknown service: $service_input"
            echo ""
            show_usage
            exit 1
        fi
        ;;
esac
