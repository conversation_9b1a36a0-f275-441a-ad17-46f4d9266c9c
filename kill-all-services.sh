#!/bin/bash

echo "🛑 Stopping all microservices..."
echo "================================="

# Array of services with their ports and names
services=(
    "Gateway Service:9999"
    "Order Service:8083"
    "Inventory Service:8082"
    "Customer Service:8081"
    "Config Service:8888"
    "Consul:8500"
)

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to get process ID using a port
get_pid_by_port() {
    local port=$1
    lsof -ti :$port 2>/dev/null
}

# Function to kill process by PID
kill_process() {
    local pid=$1
    local service_name=$2
    
    if kill $pid 2>/dev/null; then
        echo "   ✅ Process $pid killed successfully"
        
        # Wait a moment and check if process is really dead
        sleep 2
        if kill -0 $pid 2>/dev/null; then
            echo "   ⚠️  Process still running, forcing kill..."
            kill -9 $pid 2>/dev/null
            sleep 1
            if kill -0 $pid 2>/dev/null; then
                echo "   ❌ Failed to kill process $pid"
                return 1
            else
                echo "   ✅ Process $pid force-killed successfully"
            fi
        fi
        return 0
    else
        echo "   ❌ Failed to kill process $pid"
        return 1
    fi
}

# Function to stop a service
stop_service() {
    local service_name=$1
    local port=$2
    
    echo ""
    echo "🔍 Checking $service_name (port $port)..."
    
    if check_port $port; then
        local pids=$(get_pid_by_port $port)
        if [ -n "$pids" ]; then
            echo "   📍 Found running processes: $pids"
            for pid in $pids; do
                echo "   🛑 Stopping process $pid..."
                if kill_process $pid "$service_name"; then
                    echo "   ✅ $service_name stopped successfully"
                else
                    echo "   ❌ Failed to stop $service_name (PID: $pid)"
                fi
            done
        else
            echo "   ⚠️  Port $port is in use but no PID found"
        fi
    else
        echo "   ✅ $service_name is not running (port $port is free)"
    fi
}

# Function to kill all Java processes (Spring Boot services)
kill_java_services() {
    echo ""
    echo "🔍 Looking for Spring Boot services..."
    
    # Find Java processes that might be our services
    local java_pids=$(ps aux | grep java | grep -E "(spring-boot|maven|mvn)" | grep -v grep | awk '{print $2}')
    
    if [ -n "$java_pids" ]; then
        echo "   📍 Found Java/Maven processes: $java_pids"
        for pid in $java_pids; do
            local cmd=$(ps -p $pid -o cmd= 2>/dev/null)
            if [[ "$cmd" == *"spring-boot"* ]] || [[ "$cmd" == *"maven"* ]] || [[ "$cmd" == *"mvn"* ]]; then
                echo "   🛑 Stopping Java process $pid: $(echo $cmd | cut -c1-60)..."
                if kill_process $pid "Java Service"; then
                    echo "   ✅ Java process $pid stopped"
                else
                    echo "   ❌ Failed to stop Java process $pid"
                fi
            fi
        done
    else
        echo "   ✅ No Java/Maven processes found"
    fi
}

# Function to kill Consul specifically
kill_consul() {
    echo ""
    echo "🔍 Looking for Consul processes..."
    
    local consul_pids=$(ps aux | grep consul | grep -v grep | awk '{print $2}')
    
    if [ -n "$consul_pids" ]; then
        echo "   📍 Found Consul processes: $consul_pids"
        for pid in $consul_pids; do
            echo "   🛑 Stopping Consul process $pid..."
            if kill_process $pid "Consul"; then
                echo "   ✅ Consul process $pid stopped"
            else
                echo "   ❌ Failed to stop Consul process $pid"
            fi
        done
    else
        echo "   ✅ No Consul processes found"
    fi
}

echo "🔍 Checking service status..."

# Stop services in reverse order (opposite of startup)
for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    stop_service "$name" "$port"
done

# Additional cleanup for Java processes
kill_java_services

# Additional cleanup for Consul
kill_consul

echo ""
echo "🧹 Final cleanup..."

# Check if any services are still running
echo ""
echo "🔍 Final verification..."
still_running=false

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if check_port $port; then
        echo "   ⚠️  $name is still running on port $port"
        still_running=true
    else
        echo "   ✅ $name is stopped (port $port is free)"
    fi
done

echo ""
if [ "$still_running" = true ]; then
    echo "⚠️  Some services are still running. You may need to:"
    echo "   1. Check for processes manually: ps aux | grep java"
    echo "   2. Force kill remaining processes: kill -9 <PID>"
    echo "   3. Check port usage: lsof -i :<PORT>"
else
    echo "🎉 All services stopped successfully!"
fi

echo ""
echo "📋 Summary:"
echo "   • Gateway Service (9999) - Stopped"
echo "   • Order Service (8083) - Stopped"
echo "   • Inventory Service (8082) - Stopped"
echo "   • Customer Service (8081) - Stopped"
echo "   • Config Service (8888) - Stopped"
echo "   • Consul (8500) - Stopped"
echo ""
echo "💡 To start services again, use:"
echo "   ./start-and-open-services.sh"
