
# Application
spring.application.name=config-service


# Server
server.port=8888


# Use native profile for local file-based configuration (eliminates Git warnings)
spring.profiles.active=native
spring.cloud.config.server.native.search-locations=file:///home/<USER>/Bureau/magasin-workspace/config-repo


# LoadBalancer cache configuration
spring.cloud.loadbalancer.cache.enabled=true
spring.cloud.loadbalancer.cache.caffeine.spec=initialCapacity=10,maximumSize=256,expireAfterAccess=30s,recordStats



# Actuator
management.endpoints.web.exposure.include=*


# Consul configuration - ENABLED
# spring.cloud.consul.enabled=false  # Uncomment to disable <PERSON>
spring.cloud.consul.host=localhost
spring.cloud.consul.port=8500
spring.cloud.consul.discovery.health-check-interval=10s


