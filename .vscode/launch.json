{"configurations": [{"type": "java", "name": "CustomerServiceApplication", "request": "launch", "mainClass": "tn.essat.customer_service.CustomerServiceApplication", "projectName": "customer-service", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=46809 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=customer-service"}, {"type": "java", "name": "Spring Boot-ConfigServiceApplication<config-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "tn.essat.config_service.ConfigServiceApplication", "projectName": "config-service", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=42215 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=config-service"}, {"type": "java", "name": "Spring Boot-GatewayServiceApplication<gateway-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "tn.essat.gateway_service.GatewayServiceApplication", "projectName": "gateway-service", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=34659 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=gateway-service"}, {"type": "java", "name": "Spring Boot-InventoryServiceApplication<inventory-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "tn.essat.inventory_service.InventoryServiceApplication", "projectName": "inventory-service", "args": "", "envFile": "${workspaceFolder}/.env"}]}