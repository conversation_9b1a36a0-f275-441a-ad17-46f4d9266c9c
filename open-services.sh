#!/bin/bash

echo "🌐 Opening microservices in browser..."
echo "======================================"

# Array of services with their URLs in the specified order
services=(
    "Consul UI:http://localhost:8500/ui"
    "Config Service:http://localhost:8888"
    "Gateway:http://localhost:9999"
    "Customer Service:http://localhost:8081"
    "Inventory Service:http://localhost:8082"
    "Order Service:http://localhost:8083"
)

# Function to check if a service is running
check_service() {
    local url=$1
    local service_name=$2
    
    if curl -s --connect-timeout 3 "$url" > /dev/null 2>&1; then
        return 0  # Service is running
    else
        return 1  # Service is not running
    fi
}

# Function to open URL in browser
open_url() {
    local url=$1
    
    # Detect the operating system and use appropriate command
    if command -v xdg-open > /dev/null; then
        # Linux
        xdg-open "$url" 2>/dev/null &
    elif command -v open > /dev/null; then
        # macOS
        open "$url" 2>/dev/null &
    elif command -v start > /dev/null; then
        # Windows (Git Bash, WSL)
        start "$url" 2>/dev/null &
    else
        echo "❌ Cannot detect browser command for your system"
        echo "   Please open manually: $url"
        return 1
    fi
    return 0
}

echo "🔍 Checking service availability..."
echo ""

# Check and open each service
counter=1
for service in "${services[@]}"; do
    IFS=':' read -r name url <<< "$service"
    
    echo "$counter. $name"
    echo "   URL: $url"
    
    # Check if service is running (except for Consul UI which has a different health endpoint)
    if [[ "$name" == "Consul UI" ]]; then
        health_url="http://localhost:8500/v1/status/leader"
    elif [[ "$name" == "Config Service" ]]; then
        health_url="$url/actuator/health"
    elif [[ "$name" == "Gateway" ]]; then
        health_url="$url/actuator/health"
    else
        health_url="$url/actuator/health"
    fi
    
    if check_service "$health_url" "$name"; then
        echo "   Status: ✅ Running"
        if open_url "$url"; then
            echo "   Action: 🌐 Opened in browser"
        else
            echo "   Action: ❌ Failed to open browser"
        fi
    else
        echo "   Status: ❌ Not running"
        echo "   Action: ⏭️  Skipped (service not available)"
    fi
    
    echo ""
    
    # Add a small delay between opening tabs to avoid overwhelming the browser
    if [ $counter -lt ${#services[@]} ]; then
        sleep 1
    fi
    
    ((counter++))
done

echo "🎉 Browser opening process completed!"
echo ""
echo "📋 Summary of opened services:"
echo "   1. Consul UI - Service discovery and health monitoring"
echo "   2. Config Service - Centralized configuration management"
echo "   3. Gateway - API Gateway and routing"
echo "   4. Customer Service - Customer management"
echo "   5. Inventory Service - Product inventory management"
echo "   6. Order Service - Order processing and management"
echo ""
echo "💡 Tip: You can also use individual URLs:"
for service in "${services[@]}"; do
    IFS=':' read -r name url <<< "$service"
    echo "   $name: $url"
done
