package tn.essat.inventory_service;


import java.util.Random;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;

import tn.essat.inventory_service.entity.Product;
import tn.essat.inventory_service.repository.ProductRepository;




@SpringBootApplication
@EnableDiscoveryClient

public class InventoryServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(InventoryServiceApplication.class, args);
	}



	@Bean
	CommandLineRunner start(ProductRepository productRepository) {

		return args -> {

			Random random = new Random();

			for (int i=1; i <10; i++) {

				double price = Math.round((1200 + Math.random()*1000) * 100.0) / 100.0;
				productRepository.save(Product.builder().name("Computer " + i).price(price).quantity(1+random.nextInt(200)).build());
			}


			productRepository.findAll().forEach(System.out::println);
		};
	}

}
