
# Application
spring.application.name=inventory-service

# Server
server.port=8082

# Actuator
# management.endpoints.web.exposure.include=*

# Config Server
spring.config.import=optional:configserver:http://localhost:8888

# Consul configuration - ENABLED
# spring.cloud.consul.enabled=false  # Uncomment to disable <PERSON>
spring.cloud.consul.host=localhost
spring.cloud.consul.port=8500
spring.cloud.consul.discovery.health-check-interval=10s
