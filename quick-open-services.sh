#!/bin/bash

echo "🚀 Quick opening all microservices interfaces..."
echo "================================================"

# Array of services with their URLs in the specified order
services=(
    "Consul UI:http://localhost:8500/ui"
    "Config Service:http://localhost:8888"
    "Gateway:http://localhost:9999"
    "Customer Service:http://localhost:8081"
    "Inventory Service:http://localhost:8082"
    "Order Service:http://localhost:8083"
)

# Function to open URL in browser
open_url() {
    local url=$1
    
    # Detect the operating system and use appropriate command
    if command -v xdg-open > /dev/null; then
        # Linux
        xdg-open "$url" 2>/dev/null &
    elif command -v open > /dev/null; then
        # macOS
        open "$url" 2>/dev/null &
    elif command -v start > /dev/null; then
        # Windows (Git Bash, WSL)
        start "$url" 2>/dev/null &
    else
        echo "❌ Cannot detect browser command for your system"
        echo "   Please open manually: $url"
        return 1
    fi
    return 0
}

echo "🌐 Opening all services (without health checks)..."
echo ""

# Open each service
counter=1
for service in "${services[@]}"; do
    IFS=':' read -r name url <<< "$service"
    
    echo "$counter. Opening $name"
    echo "   URL: $url"
    
    if open_url "$url"; then
        echo "   Status: ✅ Opened in browser"
    else
        echo "   Status: ❌ Failed to open browser"
    fi
    
    echo ""
    
    # Add a small delay between opening tabs
    if [ $counter -lt ${#services[@]} ]; then
        sleep 1
    fi
    
    ((counter++))
done

echo "🎉 All service interfaces opened!"
echo ""
echo "📋 Opened services:"
echo "   1. Consul UI - http://localhost:8500/ui"
echo "   2. Config Service - http://localhost:8888"
echo "   3. Gateway - http://localhost:9999"
echo "   4. Customer Service - http://localhost:8081"
echo "   5. Inventory Service - http://localhost:8082"
echo "   6. Order Service - http://localhost:8083"
