#!/bin/bash

echo "🚀 Starting and opening all microservices..."
echo "============================================="

# Function to check if a service is running on a specific port
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_wait=60
    local wait_time=0
    
    echo "   ⏳ Waiting for $service_name to be ready..."
    
    while [ $wait_time -lt $max_wait ]; do
        if curl -s --connect-timeout 3 "$url" > /dev/null 2>&1; then
            echo "   ✅ $service_name is ready!"
            return 0
        fi
        sleep 2
        wait_time=$((wait_time + 2))
        echo -n "."
    done
    
    echo ""
    echo "   ⚠️  $service_name took too long to start"
    return 1
}

# Function to start a service
start_service() {
    local service_name=$1
    local port=$2
    local directory=$3
    local health_url=$4
    
    echo ""
    echo "🔧 Starting $service_name..."
    
    if check_port $port; then
        echo "   ✅ $service_name is already running on port $port"
        return 0
    fi
    
    echo "   📁 Changing to directory: $directory"
    cd "$directory" || {
        echo "   ❌ Failed to change to directory $directory"
        return 1
    }
    
    echo "   🏃 Starting $service_name with Maven..."
    mvn spring-boot:run > /dev/null 2>&1 &
    local pid=$!
    
    # Wait for service to be ready
    if wait_for_service "$health_url" "$service_name"; then
        echo "   ✅ $service_name started successfully (PID: $pid)"
        cd - > /dev/null
        return 0
    else
        echo "   ❌ $service_name failed to start properly"
        cd - > /dev/null
        return 1
    fi
}

echo "🔍 Checking current status..."

# Check Consul first
if ! check_port 8500; then
    echo ""
    echo "🔧 Starting Consul..."
    echo "   🏃 Starting Consul agent..."
    consul agent -dev -ui -client=0.0.0.0 > /dev/null 2>&1 &
    
    if wait_for_service "http://localhost:8500/v1/status/leader" "Consul"; then
        echo "   ✅ Consul started successfully"
    else
        echo "   ❌ Consul failed to start"
        exit 1
    fi
else
    echo "   ✅ Consul is already running"
fi

# Start services in order
start_service "Config Service" 8888 "config-service" "http://localhost:8888/actuator/health"
start_service "Customer Service" 8081 "customer-service" "http://localhost:8081/actuator/health"
start_service "Inventory Service" 8082 "inventory-service" "http://localhost:8082/actuator/health"
start_service "Order Service" 8083 "order-service" "http://localhost:8083/actuator/health"
start_service "Gateway Service" 9999 "gateway-service" "http://localhost:9999/actuator/health"

echo ""
echo "🎉 All services startup process completed!"
echo ""
echo "⏳ Waiting 5 seconds for services to fully initialize..."
sleep 5

echo ""
echo "🌐 Now opening all service interfaces..."
echo ""

# Run the open services script
./open-services.sh
